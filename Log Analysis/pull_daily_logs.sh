#!/bin/bash

# Usage:
# ./pull_daily_logs.sh "2025-06-17T00:00:00Z" "2025-06-19T00:00:00Z"

# Check arguments
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 START_TIME END_TIME"
    echo "Example: $0 \"2025-06-17T00:00:00Z\" \"2025-06-19T00:00:00Z\""
    exit 1
fi

# Input date range
MAIN_START_TIME="$1"
MAIN_END_TIME="$2"

# Project-specific settings
PROJECT_ID="pharmaciaty-ksa-production"
SERVICE_NAME="mw-wasfaty-integration-prod"
LOCATION="me-central1"

# Create daily logs directory if it doesn't exist
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
LOGS_DIR="${SCRIPT_DIR}/daily_logs"
mkdir -p "$LOGS_DIR"

# Function to increment date by one day
increment_date() {
    local dt="$1"
    # Extract components
    local year=$(echo "$dt" | cut -d'T' -f1 | cut -d'-' -f1)
    local month=$(echo "$dt" | cut -d'T' -f1 | cut -d'-' -f2)
    local day=$(echo "$dt" | cut -d'T' -f1 | cut -d'-' -f3)
    local time=$(echo "$dt" | cut -d'T' -f2)

    # Use macOS date to increment
    # Convert to seconds since epoch, add 86400 seconds (1 day), then format back
    local new_date=$(date -j -v+1d -f "%Y-%m-%dT%H:%M:%SZ" "$dt" "+%Y-%m-%dT%H:%M:%SZ")
    echo "$new_date"
}

# Parse start date to get just the date part in YYYY-MM-DD format
extract_date_part() {
    date -j -f "%Y-%m-%dT%H:%M:%SZ" "$1" "+%Y-%m-%d"
}

# Start with the initial start time
CURRENT_DATE_TIME="$MAIN_START_TIME"

echo "Starting log extraction for period $MAIN_START_TIME to $MAIN_END_TIME"
echo "------------------------------------------------------------"

# Loop until we reach or exceed the end time
while [[ "$CURRENT_DATE_TIME" < "$MAIN_END_TIME" ]]; do
    # Calculate next day's date time (for the end time of this iteration)
    NEXT_DATE_TIME=$(increment_date "$CURRENT_DATE_TIME")

    echo "Debug: Current=$CURRENT_DATE_TIME, Next=$NEXT_DATE_TIME" # Debug line

    # Don't go beyond the main end time
    if [[ "$NEXT_DATE_TIME" > "$MAIN_END_TIME" ]]; then
        NEXT_DATE_TIME="$MAIN_END_TIME"
    fi

    # Extract date part for the file name
    FILE_DATE=$(extract_date_part "$CURRENT_DATE_TIME")
    OUTPUT_FILE="${LOGS_DIR}/logs_${FILE_DATE}.csv"

    # Define log filter for this day
    FILTER="resource.type=\"cloud_run_revision\" \
    resource.labels.service_name=\"$SERVICE_NAME\" \
    resource.labels.location=\"$LOCATION\" \
    severity>=DEFAULT \
    jsonPayload.message=\"POST /api/v1/wasfaty/webhook/notifyNewOrder\" \
    jsonPayload.response.body.message=\"Successfully received new order\" \
    timestamp>=\"$CURRENT_DATE_TIME\" AND timestamp<\"$NEXT_DATE_TIME\""

    # Execute log query for this day
    echo "Fetching logs from $CURRENT_DATE_TIME to $NEXT_DATE_TIME..."
    if gcloud logging read "$FILTER" \
      --project="$PROJECT_ID" \
      --format="csv(timestamp,jsonPayload.request.body.branchLicense,jsonPayload.request.body.deliveryDate,jsonPayload.request.body.deliveryTimeSlotEndTime,jsonPayload.request.body.deliveryTimeSlotStartTime,jsonPayload.request.body.erxReference,jsonPayload.request.body.isPickup,jsonPayload.request.body.orderId,jsonPayload.request.body.patient.firstName,jsonPayload.request.body.patient.gender,jsonPayload.request.body.patient.lastName,jsonPayload.request.body.patient.memberId,jsonPayload.request.body.patient.nationalId,jsonPayload.request.body.shippingAddress.area,jsonPayload.request.body.shippingAddress.coordinates.latitude,jsonPayload.request.body.shippingAddress.coordinates.longitude)" > "$OUTPUT_FILE"; then
        echo "✓ Logs successfully exported to $OUTPUT_FILE"
    else
        echo "✗ Error: Failed to export logs for $CURRENT_DATE_TIME to $NEXT_DATE_TIME"
    fi

    echo "------------------------------------------------------------"

    # Move to the next day
    CURRENT_DATE_TIME="$NEXT_DATE_TIME"
done

echo "Log extraction completed for all days in the specified range."
