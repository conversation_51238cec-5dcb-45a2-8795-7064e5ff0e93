#%%
# Install required libraries if not already installed
# !pip install --upgrade pip
# !pip install psycopg2-binary sqlalchemy pandas python-dotenv --quiet

import pandas as pd
# Import our custom data module
import sys
import os

sys.path.append(os.path.dirname(os.getcwd()))
from data import Settings, get_leads_data, get_todays_leads
from data import get_activities_data

#%%
# Load settings from environment variables or .env file
# Create a .env file based on .env.example and place it in the same directory
settings = Settings(env_file='.env')  # You can specify a custom path if needed
db_config = settings.get_db_config()

# Filters for Missed leads
status = ['unavailable', 'partially-available']
date = '2025-06-01'

# Example: Extract leads data (limit to 10 rows initially)
leads_df = get_leads_data(db_config,
                          statuses=status,
                          start_date=date,
                          )
#%%
#Add the calculated colums whcih check how many activites were missing from the
leads_df['missing_activities'] = leads_df['activity_count'] - leads_df['available_activity_count']

# drop columns which are unnecessary here is_accepted,is_allocated, is_approved,is_partially_approved, is_claimed
leads_df = leads_df.drop(columns=['is_accepted', 'is_allocated', 'is_approved', 'is_partially_approved', 'is_claimed'])

#%%
leads_df.head(100)
#%%
# Create a config for wasfatydb (activities DB)
wasfatydb_config = {
    "db_host": "************",  # Update if different for wasfatydb
    "db_port": "5432",
    "db_name": "wasfatydb",      # Use the correct DB name for activities
    "db_user": "product_service_user",
    "db_pass": "_&kJCD>>LJ<oTkUp"
}

# Fetch activities for the leads in leads_df
lead_ids = leads_df['order_number'].unique().tolist()

activities_df = get_activities_data(
    wasfatydb_config,
    lead_ids=lead_ids,  # list of order numbers from your leads_df
    start_date=date
)
#%%
activities_df.head(10)
#%%
# if activities_df['tags'] has express_delivery then set the delivery_type to express else if it is scheduled_delivery then scheduled
activities_df['delivery_type'] = activities_df['tags'].apply(lambda x: 'express' if 'express_delivery' in x else 'scheduled' if 'scheduled_delivery' in x else None)

activities_df.head()


#%%
# Group by trade code and count the number of activities per trade code
tradecode_col = "Trade Drugs - InternalId__drugCode"
top_activities_by_tradecode = (
    activities_df.groupby(tradecode_col)
    .size()
    .reset_index(name='activity_count')
    .sort_values('activity_count', ascending=False)
    .head(100)
)
top_activities_by_tradecode
#%%
# Join leads_df and activities_df on order_number/orderNumber
joined_df = pd.merge(
    leads_df,
    activities_df,
    left_on='order_number',
    right_on='orderNumber',
    how='left',
    suffixes=('_lead', '_activity')
)

# Preview the joined DataFrame
joined_df.head(10)
#%%
# add the date part from created_at to the dataframe
joined_df['created_at'] = pd.to_datetime(joined_df['createdAt'])
joined_df['date'] = joined_df['created_at'].dt.date

joined_df.to_csv('activities latest.csv', index=False)


#%%
# remove duplicates by erxReference and Trade Drugs - InternalId__drugCode and date
joined_df = joined_df.drop_duplicates(subset=['erxReference', 'Trade Drugs - InternalId__drugCode', 'date'], keep='first')
#%%
# top generic codes by number of unique orders aggrigarted on date
generic_code_col = "Activities - OrderId__genericCode"
order_number_col = "order_number"
date_col = "date"

top_generic_codes_by_date = (
    joined_df.groupby([generic_code_col, date_col])[order_number_col]
    .nunique()
    .reset_index(name='unique_lost_orders')
    .sort_values('unique_lost_orders', ascending=False)
)
top_generic_codes_by_date

#%%
joined_df.head(10)
#%%
# Dataframe of all the generic codes and their names and their skus along with the tradecode
generic_code_col = "Activities - OrderId__genericCode"
drug_name_col = "Drugs - DrugCode__name"
drug_sku_col = "Drugs - DrugCode__sku"
is_available_col = 'Trade Drugs - InternalId__isAvailable'
tradecode_col = "Trade Drugs - InternalId__drugCode"

# Create a dataframe with generic code, tradecode, name, and SKU
generic_tradecode_mapping = (
    joined_df[[generic_code_col, tradecode_col, drug_name_col, drug_sku_col]]
    .drop_duplicates()
    .sort_values(by=generic_code_col)
    .reset_index(drop=True)
)

# Save to CSV if needed
# generic_tradecode_mapping.to_csv('generic_tradecode_mapping.csv', index=False)
generic_tradecode_mapping

#%%
# Find the top 100 generic codes associated with lost orders and their unique order count
# group them by the date as well
generic_code_col = "Activities - OrderId__genericCode"
order_number_col = "order_number"

top_generic_codes = (
    joined_df.groupby(generic_code_col)[order_number_col]
    .nunique()
    .reset_index(name='unique_lost_orders')
    .sort_values('unique_lost_orders', ascending=False)
    .head(100)
    .rename(columns={generic_code_col: "generic_code"})
)
top_generic_codes
#%%
# let's get he names of the these top_generic_codes the name are in the Trade Drugs - DrugCode__name column
drug_name_col = "Trade Drugs - DrugCode__name"
drug_sku_col = "Drugs - DrugCode__sku"
top_generic_codes_with_names = pd.merge(
    top_generic_codes,
    activities_df,
    left_on='generic_code',
    right_on='Activities - OrderId__genericCode',
    how='left',
    suffixes=('_lead', '_activity')
)
# filter the dataframe for rows whcih are mapped where Is Mapped? is Yes
top_generic_codes_with_names = top_generic_codes_with_names[top_generic_codes_with_names['Is Mapped?'] == 'Yes']


# Drop duplicates based on generic_code, unique_lost_orders and drug_name_col, drug_sku_col  keep the first one
top_generic_codes_with_names = top_generic_codes_with_names.drop_duplicates(subset=['generic_code', 'unique_lost_orders', 'Drugs - DrugCode__name', drug_sku_col], keep='first')

top_generic_codes_with_names.to_csv('top_generic_codes_with_names.csv', index=False)




#%%
# lets get the total number of activities for each of these generic codes
top_generic_codes_with_names['total_activities'] = top_generic_codes_with_names['generic_code'].map(
    top_activities_by_tradecode.set_index('Trade Drugs - InternalId__drugCode')['activity_count']
)
top_generic_codes_with_names
#%%
# lets get the percentage of activities that are lost for each of these generic codes