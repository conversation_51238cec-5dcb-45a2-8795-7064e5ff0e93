#%%
from datetime import timedelta

import pandas as pd

from data import get_consolidated_orders
from data import get_wasfaty_raw_leads
#%%
# Create a config for wasfatydb (activities DB)
wasfatydb_config = {
    "db_host": "************",  # Update if different for wasfatydb
    "db_port": "5432",
    "db_name": "wasfatydb",  # Use the correct DB name for activities
    "db_user": "product_service_user",
    "db_pass": "_&kJCD>>LJ<oTkUp"
}

#%%
# Get the raw leads data from Wasfaty Microservice
leads = get_wasfaty_raw_leads(wasfatydb_config,
                              start_date='2025-06-01',
                              end_date='2025-06-30')
# Convert to DataFrame
leads_df = pd.DataFrame(leads)
#%%
# Apply the order type based on the tags if it is express_delivery or scheduled_delivery then apply the type express or scheduled
leads_df['order_type'] = leads_df['tags'].apply(
    lambda x: 'express' if 'express_delivery' in x else 'scheduled' if 'scheduled_delivery' in x else None)

# Apply the warehouse based on the tags if it 7 then we call it Damman if its is 10 we call it Riyadh
leads_df['warehouse'] = leads_df['warehouseIds'].apply(
    lambda x: 'Dammam' if 7 in x else 'Riyadh' if 10 in x else None)

#%%
# Safely cast time objects to strings
leads_df['deliveryTimeSlotStartTime'] = leads_df['deliveryTimeSlotStartTime'].apply(
    lambda x: x.strftime('%H:%M:%S') if isinstance(x, pd._libs.tslibs.timestamps.Timestamp) or isinstance(x,
                                                                                                          pd.Timestamp) else (
        x.strftime('%H:%M:%S') if hasattr(x, 'strftime') else x)
)

leads_df['deliveryTimeSlotEndTime'] = leads_df['deliveryTimeSlotEndTime'].apply(
    lambda x: x.strftime('%H:%M:%S') if isinstance(x, pd._libs.tslibs.timestamps.Timestamp) or isinstance(x,
                                                                                                          pd.Timestamp) else (
        x.strftime('%H:%M:%S') if hasattr(x, 'strftime') else x)
)

# Convert original delivery time slot columns to timedelta
leads_df['start_time'] = pd.to_timedelta(leads_df['deliveryTimeSlotStartTime'], errors='coerce')
leads_df['end_time'] = pd.to_timedelta(leads_df['deliveryTimeSlotEndTime'], errors='coerce')


# Recalculate delivery window based on updated time columns
def recalculate_delivery_window(row):
    if row['order_type'] == 'express':
        start = row['createdAt']
        end = start + timedelta(hours=3) if pd.notnull(start) else pd.NaT
    elif pd.notnull(row['deliveryDate']) and pd.notnull(row['start_time']) and pd.notnull(row['end_time']):
        start = row['deliveryDate'] + row['start_time']
        end = row['deliveryDate'] + row['end_time']
    else:
        start = end = pd.NaT
    return pd.Series([start, end], index=['delivery_start_datetime', 'delivery_end_datetime'])


# Apply updated logic
leads_df[['delivery_start_datetime', 'delivery_end_datetime']] = leads_df.apply(recalculate_delivery_window, axis=1)

# drop the columns that are not needed only keep delivery_start_datetime, delivery_end_datetime, order_type, warehouse, orderId,erxReference,orderNumber,status,branchLicense
leads_df = leads_df[
    ['orderId', 'erxReference', 'orderNumber', 'delivery_start_datetime', 'delivery_end_datetime', 'order_type',
     'warehouse', 'status', 'branchLicense']]
leads_df['order_number'] = leads_df['orderNumber']

#%%
# Get consolidated order data for this month and then left join it with the leads_df
DWH_CONFIGS = wasfatydb_config.copy()  # Copy the config to avoid modifying the original
DWH_CONFIGS['db_name'] = 'General DWH'  # Use the correct DB name for activities

consolidated_orders = get_consolidated_orders(DWH_CONFIGS,
                                              start_date='2025-06-01',
                                              end_date='2025-06-30')

#%%
# left join the consolidated orders with the leads_df
final_df = leads_df.merge(consolidated_orders, how='left', left_on='order_number', right_on='order_number',
                          suffixes=('', '_consolidated'))

#%%
# filter order which heave either first_attempt_at or delivered_at
final_df = final_df[
    (final_df['first_attempt_at'].notnull()) | (final_df['delivered_at'].notnull())]

#%%
# create a column called sla_date, if first_attempt_at is not null then sla_date = first_attempt_at else sla_date = delivered_at
final_df['sla_date'] = final_df.apply(
    lambda x: x['first_attempt_at'] if pd.notnull(x['first_attempt_at']) else x['delivered_at'], axis=1)



#%%
final_df.to_csv('wasfaty_sla_data.csv', index=False)
#%%
# calculate if SLA compliance
# if the order is express then the sla_date should be less than or equal to  createdAt + 3 hours
# if the order is scheduled then the sla_date should be between delivery_start_datetime and delivery_end_datetime
# Reapply SLA compliance logic
def check_sla_compliance(row):
    try:
        sla_time = pd.to_datetime(row['sla_date'], errors='coerce')
        if row['order_type'] == 'express':
            return sla_time <= row['created_at'] + timedelta(hours=3) if pd.notnull(sla_time) and pd.notnull(row['created_at']) else False
        elif pd.notnull(row['delivery_start_datetime']) and pd.notnull(row['delivery_end_datetime']):
            return row['delivery_start_datetime'] <= sla_time <= row['delivery_end_datetime'] if pd.notnull(sla_time) else False
        return False
    except Exception:
        return False

final_df['sla_compliance'] = final_df.apply(check_sla_compliance, axis=1)




#%%
final_df
#%%
