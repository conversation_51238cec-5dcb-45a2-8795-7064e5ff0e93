#!/usr/bin/env python3
"""
Daily Order Loss Analysis Script
Generates a comprehensive report for the commercial team about daily order losses
due to medication unavailability.

Usage: python daily_order_loss_report.py
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data(csv_file='activities.csv'):
    """Load and prepare the activities data for analysis."""
    print("📊 Loading and preparing data...")
    
    df = pd.read_csv(csv_file)
    
    # Convert date columns
    df['created_at'] = pd.to_datetime(df['created_at'])
    df['createdAt'] = pd.to_datetime(df['createdAt'])
    
    # Extract date and time components
    df['date'] = df['created_at'].dt.date
    df['hour'] = df['created_at'].dt.hour
    df['day_name'] = df['created_at'].dt.day_name()
    
    return df

def generate_daily_summary(df):
    """Generate daily order loss summary."""
    print("\n🚨 DAILY ORDER LOSS SUMMARY")
    print("=" * 40)
    
    # Calculate daily order losses
    daily_orders = df.groupby('date').agg({
        'order_number': 'nunique',
        'Trade Drugs - InternalId__isAvailable': lambda x: (x == False).sum(),
        'Activities - OrderId__genericCode': 'count'
    }).rename(columns={
        'order_number': 'total_orders',
        'Trade Drugs - InternalId__isAvailable': 'unavailable_items',
        'Activities - OrderId__genericCode': 'total_items'
    })
    
    daily_orders['availability_rate'] = ((daily_orders['total_items'] - daily_orders['unavailable_items']) / daily_orders['total_items'] * 100).round(2)
    daily_orders['unavailability_rate'] = (daily_orders['unavailable_items'] / daily_orders['total_items'] * 100).round(2)
    
    print(f"Average daily orders lost: {daily_orders['total_orders'].mean():.0f}")
    print(f"Average unavailable items per day: {daily_orders['unavailable_items'].mean():.0f}")
    print(f"Average availability rate: {daily_orders['availability_rate'].mean():.1f}%")
    print(f"Average unavailability rate: {daily_orders['unavailability_rate'].mean():.1f}%")
    
    # Display recent days
    print("\n📅 Recent Daily Performance:")
    recent_days = daily_orders.tail(7).copy()
    recent_days.index = pd.to_datetime(recent_days.index)
    recent_days['day_name'] = recent_days.index.strftime('%A')
    print(recent_days[['day_name', 'total_orders', 'unavailable_items', 'unavailability_rate']].to_string())
    
    return daily_orders

def analyze_missing_medications(df):
    """Analyze most frequently unavailable medications."""
    print("\n💊 TOP MISSING MEDICATIONS - CRITICAL FOR COMMERCIAL TEAM")
    print("=" * 60)
    
    unavailable_meds = df[df['Trade Drugs - InternalId__isAvailable'] == False]
    
    # Top medications by order count affected
    top_missing_by_orders = (
        unavailable_meds.groupby('Activities - OrderId__genericCode')['order_number']
        .nunique()
        .reset_index(name='affected_orders')
        .sort_values('affected_orders', ascending=False)
        .head(20)
    )
    
    # Top medications by frequency of unavailability
    top_missing_by_frequency = (
        unavailable_meds.groupby('Activities - OrderId__genericCode')
        .size()
        .reset_index(name='unavailable_count')
        .sort_values('unavailable_count', ascending=False)
        .head(20)
    )
    
    print("\n🎯 Top 10 Medications by Number of Orders Affected:")
    for i, row in top_missing_by_orders.head(10).iterrows():
        print(f"{i + 1:2d}. {row['Activities - OrderId__genericCode']}: {row['affected_orders']} orders lost")
    
    print("\n🔄 Top 10 Medications by Frequency of Unavailability:")
    for i, row in top_missing_by_frequency.head(10).iterrows():
        print(f"{i + 1:2d}. {row['Activities - OrderId__genericCode']}: {row['unavailable_count']} times unavailable")
    
    return top_missing_by_orders, top_missing_by_frequency

def analyze_branch_performance(df):
    """Analyze branch-wise impact."""
    print("\n🏪 BRANCH-WISE ORDER LOSS ANALYSIS")
    print("=" * 40)
    
    branch_analysis = df.groupby('branch').agg({
        'order_number': 'nunique',
        'Trade Drugs - InternalId__isAvailable': lambda x: (x == False).sum(),
        'Activities - OrderId__genericCode': 'count'
    }).rename(columns={
        'order_number': 'total_orders',
        'Trade Drugs - InternalId__isAvailable': 'unavailable_items',
        'Activities - OrderId__genericCode': 'total_items'
    })
    
    branch_analysis['unavailability_rate'] = (branch_analysis['unavailable_items'] / branch_analysis['total_items'] * 100).round(2)
    branch_analysis = branch_analysis.sort_values('total_orders', ascending=False)
    
    print(f"Total branches affected: {len(branch_analysis)}")
    print(f"Average orders per branch: {branch_analysis['total_orders'].mean():.1f}")
    print(f"Average unavailability rate per branch: {branch_analysis['unavailability_rate'].mean():.1f}%")
    
    print("\n🔝 Top 10 Branches by Order Volume (Most Affected):")
    top_branches = branch_analysis.head(10)
    for idx, (branch, row) in enumerate(top_branches.iterrows(), 1):
        print(f"{idx:2d}. {branch}: {row['total_orders']} orders, {row['unavailability_rate']:.1f}% unavailable")
    
    print("\n⚠️  Top 10 Branches by Highest Unavailability Rate:")
    worst_branches = branch_analysis.sort_values('unavailability_rate', ascending=False).head(10)
    for idx, (branch, row) in enumerate(worst_branches.iterrows(), 1):
        print(f"{idx:2d}. {branch}: {row['unavailability_rate']:.1f}% unavailable ({row['total_orders']} orders)")
    
    return branch_analysis

def analyze_time_patterns(df):
    """Analyze time-based patterns."""
    print("\n⏰ TIME-BASED ORDER LOSS PATTERNS")
    print("=" * 40)
    
    # Hourly analysis
    hourly_analysis = df.groupby('hour').agg({
        'order_number': 'nunique',
        'Trade Drugs - InternalId__isAvailable': lambda x: (x == False).sum(),
        'Activities - OrderId__genericCode': 'count'
    }).rename(columns={
        'order_number': 'total_orders',
        'Trade Drugs - InternalId__isAvailable': 'unavailable_items',
        'Activities - OrderId__genericCode': 'total_items'
    })
    
    hourly_analysis['unavailability_rate'] = (hourly_analysis['unavailable_items'] / hourly_analysis['total_items'] * 100).round(2)
    
    # Day of week analysis
    daily_analysis = df.groupby('day_name').agg({
        'order_number': 'nunique',
        'Trade Drugs - InternalId__isAvailable': lambda x: (x == False).sum(),
        'Activities - OrderId__genericCode': 'count'
    }).rename(columns={
        'order_number': 'total_orders',
        'Trade Drugs - InternalId__isAvailable': 'unavailable_items',
        'Activities - OrderId__genericCode': 'total_items'
    })
    
    daily_analysis['unavailability_rate'] = (daily_analysis['unavailable_items'] / daily_analysis['total_items'] * 100).round(2)
    
    # Reorder days of week
    day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    daily_analysis = daily_analysis.reindex([day for day in day_order if day in daily_analysis.index])
    
    print("\n🕐 Peak Hours for Order Losses:")
    peak_hours = hourly_analysis.sort_values('total_orders', ascending=False).head(5)
    for hour, row in peak_hours.iterrows():
        print(f"   {hour:2d}:00 - {row['total_orders']} orders ({row['unavailability_rate']:.1f}% unavailable)")
    
    print("\n📅 Day of Week Performance:")
    for day, row in daily_analysis.iterrows():
        print(f"   {day}: {row['total_orders']} orders ({row['unavailability_rate']:.1f}% unavailable)")
    
    return hourly_analysis, daily_analysis

def calculate_financial_impact(df):
    """Calculate financial impact estimation."""
    print("\n💰 FINANCIAL IMPACT ANALYSIS")
    print("=" * 40)
    
    # Note: These are estimated values - adjust based on actual business metrics
    ESTIMATED_ORDER_VALUE = 150  # Average order value in SAR
    ESTIMATED_MARGIN = 0.25      # 25% profit margin
    
    total_orders_lost = df['order_number'].nunique()
    total_items_unavailable = (df['Trade Drugs - InternalId__isAvailable'] == False).sum()
    total_items = len(df)
    
    # Calculate financial impact
    estimated_revenue_loss = total_orders_lost * ESTIMATED_ORDER_VALUE
    estimated_profit_loss = estimated_revenue_loss * ESTIMATED_MARGIN
    
    # Daily averages
    days_in_analysis = (df['date'].max() - df['date'].min()).days + 1
    daily_revenue_loss = estimated_revenue_loss / days_in_analysis
    daily_profit_loss = estimated_profit_loss / days_in_analysis
    
    # Monthly and yearly projections
    monthly_revenue_loss = daily_revenue_loss * 30
    monthly_profit_loss = daily_profit_loss * 30
    yearly_revenue_loss = daily_revenue_loss * 365
    yearly_profit_loss = daily_profit_loss * 365
    
    print(f"Analysis period: {days_in_analysis} days")
    print(f"Assumptions: Avg order value = {ESTIMATED_ORDER_VALUE} SAR, Profit margin = {ESTIMATED_MARGIN*100}%")
    print()
    print("📊 TOTAL IMPACT (Analysis Period):")
    print(f"   Orders lost: {total_orders_lost:,}")
    print(f"   Items unavailable: {total_items_unavailable:,}")
    print(f"   Estimated revenue loss: {estimated_revenue_loss:,.0f} SAR")
    print(f"   Estimated profit loss: {estimated_profit_loss:,.0f} SAR")
    print()
    print("📈 DAILY AVERAGES:")
    print(f"   Orders lost per day: {total_orders_lost/days_in_analysis:.1f}")
    print(f"   Revenue loss per day: {daily_revenue_loss:,.0f} SAR")
    print(f"   Profit loss per day: {daily_profit_loss:,.0f} SAR")
    print()
    print("🎯 PROJECTIONS:")
    print(f"   Monthly revenue loss: {monthly_revenue_loss:,.0f} SAR")
    print(f"   Monthly profit loss: {monthly_profit_loss:,.0f} SAR")
    print(f"   Yearly revenue loss: {yearly_revenue_loss:,.0f} SAR")
    print(f"   Yearly profit loss: {yearly_profit_loss:,.0f} SAR")
    
    return {
        'daily_revenue_loss': daily_revenue_loss,
        'monthly_revenue_loss': monthly_revenue_loss,
        'yearly_revenue_loss': yearly_revenue_loss,
        'total_orders_lost': total_orders_lost,
        'days_in_analysis': days_in_analysis
    }

def generate_executive_summary(df, top_missing_by_orders, branch_analysis, financial_impact):
    """Generate executive summary and recommendations."""
    print("\n📋 EXECUTIVE SUMMARY - DAILY ORDER LOSS ANALYSIS")
    print("=" * 60)
    
    total_unique_orders = df['order_number'].nunique()
    total_unique_meds = df['Activities - OrderId__genericCode'].nunique()
    total_unique_branches = df['branch'].nunique()
    overall_unavailability_rate = (df['Trade Drugs - InternalId__isAvailable'] == False).sum() / len(df) * 100
    
    # Top problematic medication
    top_problem_med = top_missing_by_orders.iloc[0]
    top_problem_branch = branch_analysis.sort_values('unavailability_rate', ascending=False).iloc[0]
    
    print(f"Analysis Period: {df['date'].min()} to {df['date'].max()} ({financial_impact['days_in_analysis']} days)")
    print()
    print("🔢 KEY METRICS:")
    print(f"   • Total orders lost: {total_unique_orders:,}")
    print(f"   • Unique medications involved: {total_unique_meds:,}")
    print(f"   • Branches affected: {total_unique_branches:,}")
    print(f"   • Overall unavailability rate: {overall_unavailability_rate:.1f}%")
    print(f"   • Daily average orders lost: {total_unique_orders/financial_impact['days_in_analysis']:.1f}")
    print()
    print("💰 FINANCIAL IMPACT:")
    print(f"   • Estimated daily revenue loss: {financial_impact['daily_revenue_loss']:,.0f} SAR")
    print(f"   • Estimated monthly revenue loss: {financial_impact['monthly_revenue_loss']:,.0f} SAR")
    print(f"   • Estimated yearly revenue loss: {financial_impact['yearly_revenue_loss']:,.0f} SAR")
    print()
    print("🎯 TOP ISSUES:")
    print(f"   • Most problematic medication: {top_problem_med['Activities - OrderId__genericCode'][:40]}...")
    print(f"     (Affects {top_problem_med['affected_orders']} orders)")
    print(f"   • Branch with highest unavailability: {top_problem_branch.name}")
    print(f"     ({top_problem_branch['unavailability_rate']:.1f}% unavailable)")
    print()
    print("📈 RECOMMENDATIONS FOR COMMERCIAL TEAM:")
    print("   1. 🎯 PRIORITY ACTIONS:")
    print(f"      • Focus on top 10 medications causing {top_missing_by_orders.head(10)['affected_orders'].sum()} order losses")
    print(f"      • Address inventory issues in top 5 branches with highest order volumes")
    print(f"      • Implement daily monitoring for medications with <50% availability rate")
    print()
    print("   2. 📊 OPERATIONAL IMPROVEMENTS:")
    print("      • Set up automated alerts for critical medication stock levels")
    print("      • Establish backup supplier relationships for high-demand medications")
    print("      • Implement predictive inventory management based on historical patterns")
    print()
    print("   3. 💼 BUSINESS IMPACT MITIGATION:")
    print("      • Develop alternative medication suggestions for unavailable items")
    print("      • Create customer communication templates for stock-out situations")
    print(f"      • Target reducing daily order losses by 50% to save ~{financial_impact['daily_revenue_loss']/2:,.0f} SAR/day")
    print()
    print("📞 NEXT STEPS:")
    print("   • Schedule weekly review meetings with supply chain team")
    print("   • Implement real-time inventory tracking dashboard")
    print("   • Set up customer retention programs for affected orders")
    print("   • Monitor progress with monthly order loss reduction targets")

def main():
    """Main function to run the complete analysis."""
    print("📊 Daily Order Loss Analysis - Commercial Team Report")
    print("=" * 60)
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Load data
    df = load_and_prepare_data()
    
    print(f"\n📈 Dataset Overview:")
    print(f"Total records: {len(df):,}")
    print(f"Date range: {df['date'].min()} to {df['date'].max()}")
    print(f"Unique orders: {df['order_number'].nunique():,}")
    print(f"Unique branches: {df['branch'].nunique():,}")
    print(f"Unique medications: {df['Activities - OrderId__genericCode'].nunique():,}")
    
    # Run analysis
    daily_summary = generate_daily_summary(df)
    top_missing_by_orders, top_missing_by_frequency = analyze_missing_medications(df)
    branch_analysis = analyze_branch_performance(df)
    hourly_analysis, daily_analysis = analyze_time_patterns(df)
    financial_impact = calculate_financial_impact(df)
    generate_executive_summary(df, top_missing_by_orders, branch_analysis, financial_impact)
    
    print(f"\n✅ Analysis completed successfully!")
    print(f"Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
