#                   --- THIS FILE IS AUTO-GENERATED ---
# Modifications will be overwitten the next time code generation run.

from plotly.basedatatypes import BaseTraceHierarchyType as _BaseTraceHierarchyType
import copy as _copy


class Line(_BaseTraceHierarchyType):

    _parent_path_str = "scattergl"
    _path_str = "scattergl.line"
    _valid_props = {"color", "dash", "shape", "width"}

    @property
    def color(self):
        """
        Sets the line color.

        The 'color' property is a color and may be specified as:
          - A hex string (e.g. '#ff0000')
          - An rgb/rgba string (e.g. 'rgb(255,0,0)')
          - An hsl/hsla string (e.g. 'hsl(0,100%,50%)')
          - An hsv/hsva string (e.g. 'hsv(0,100%,100%)')
          - A named CSS color: see https://plotly.com/python/css-colors/ for a list

        Returns
        -------
        str
        """
        return self["color"]

    @color.setter
    def color(self, val):
        self["color"] = val

    @property
    def dash(self):
        """
        Sets the style of the lines.

        The 'dash' property is an enumeration that may be specified as:
          - One of the following enumeration values:
                ['dash', 'dashdot', 'dot', 'longdash', 'longdashdot',
                'solid']

        Returns
        -------
        Any
        """
        return self["dash"]

    @dash.setter
    def dash(self, val):
        self["dash"] = val

    @property
    def shape(self):
        """
        Determines the line shape. The values correspond to step-wise
        line shapes.

        The 'shape' property is an enumeration that may be specified as:
          - One of the following enumeration values:
                ['linear', 'hv', 'vh', 'hvh', 'vhv']

        Returns
        -------
        Any
        """
        return self["shape"]

    @shape.setter
    def shape(self, val):
        self["shape"] = val

    @property
    def width(self):
        """
        Sets the line width (in px).

        The 'width' property is a number and may be specified as:
          - An int or float in the interval [0, inf]

        Returns
        -------
        int|float
        """
        return self["width"]

    @width.setter
    def width(self, val):
        self["width"] = val

    @property
    def _prop_descriptions(self):
        return """\
        color
            Sets the line color.
        dash
            Sets the style of the lines.
        shape
            Determines the line shape. The values correspond to
            step-wise line shapes.
        width
            Sets the line width (in px).
        """

    def __init__(
        self, arg=None, color=None, dash=None, shape=None, width=None, **kwargs
    ):
        """
        Construct a new Line object

        Parameters
        ----------
        arg
            dict of properties compatible with this constructor or
            an instance of
            :class:`plotly.graph_objs.scattergl.Line`
        color
            Sets the line color.
        dash
            Sets the style of the lines.
        shape
            Determines the line shape. The values correspond to
            step-wise line shapes.
        width
            Sets the line width (in px).

        Returns
        -------
        Line
        """
        super().__init__("line")
        if "_parent" in kwargs:
            self._parent = kwargs["_parent"]
            return

        if arg is None:
            arg = {}
        elif isinstance(arg, self.__class__):
            arg = arg.to_plotly_json()
        elif isinstance(arg, dict):
            arg = _copy.copy(arg)
        else:
            raise ValueError(
                """\
The first argument to the plotly.graph_objs.scattergl.Line
constructor must be a dict or
an instance of :class:`plotly.graph_objs.scattergl.Line`"""
            )

        self._skip_invalid = kwargs.pop("skip_invalid", False)
        self._validate = kwargs.pop("_validate", True)

        self._set_property("color", arg, color)
        self._set_property("dash", arg, dash)
        self._set_property("shape", arg, shape)
        self._set_property("width", arg, width)
        self._process_kwargs(**dict(arg, **kwargs))
        self._skip_invalid = False
